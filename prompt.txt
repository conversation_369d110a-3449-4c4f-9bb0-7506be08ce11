Tu es un **Senior Firebase Engineer**, méticuleux et orienté tests.
Ta source de vérité principale pour TOUTES les conventions est le fichier @.augment-guidelines. Respecte-le scrupuleusement.
Code en **TypeScript**, Node 18, ES modules.

Nous allons procéder en deux phases. Ne passe à la phase 2 que lorsque j'aurai validé ton plan.
---
### PHASE 1 : PLANIFICATION

**Tâche :** CF_TRIGGEREMPRUNTDEPARTCF_CALLABLE - CF triggerEmpruntDepartCF (Callable)
**Objectif :** Produire un plan d'action détaillé pour implémenter cette fonctionnalité.
Utilise l'outil **`sequential_thinking`** pour organiser tes pensées, et **`use context7`** pour baser ta réflexion sur des informations à jour.

**Contexte de Référence :**
- `@docs/Architecture_SIGMA_v1.2.md`
- `@docs/Interface_SIGMA_v1.0.md`

**Ton plan doit inclure :**
1. La liste des fichiers à créer/modifier.
2. La signature de la fonction et la structure des données (en utilisant Zod pour la validation).
3. La logique principale (vérification des permissions, transaction Firestore, gestion des erreurs avec HttpsError).
4. La stratégie de test (quels cas couvrir dans Jest).

**Ne génère aucun code de production à ce stade. Présente uniquement ton plan et attends ma validation.**
---
### PHASE 2 : IMPLÉMENTATION & VALIDATION (après ma validation du plan)

**Objectif :** Implémenter le plan que tu as proposé.
**Fichiers cibles :** firebase/functions/emprunts.js firebase/functions/emprunts.test.js
**Critères d’acceptation :** La commande `npm test -- firebase/functions/emprunts.test.js` doit réussir. Tu dois écrire les tests nécessaires pour cela, avec une couverture Jest ≥ 80 %.

**Maintenant, exécute le plan validé.**
