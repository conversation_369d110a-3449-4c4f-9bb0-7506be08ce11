rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // --- Fonctions Utilitaires ---
    function isAuthenticated() {
      return request.auth != null;
    }

    function hasRole(role) {
      return isAuthenticated() && (request.auth.token.role == role || request.auth.token.role == 'admin');
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // --- Fonctions de Validation de Données ---
    function isValidEmpruntData(data) {
      return data.nom is string && data.nom.size() > 0 &&
             data.lieu is string && data.lieu.size() > 0 &&
             data.dateDepart is timestamp &&
             data.dateRetour is timestamp &&
             data.dateRetour > data.dateDepart &&
             data.statut in ['Pas prêt', 'Prêt', 'Parti', 'Revenu', 'Inventorié'];
    }

    // --- RÈGLES PAR COLLECTION ---

    match /users/{userId} {
      allow get: if isOwner(userId) || hasRole('admin');
      allow list: if hasRole('admin');
      allow create: if false; // Géré par Cloud Function
      allow delete: if false; // Géré par Cloud Function

      // L'admin peut tout faire. L'utilisateur peut modifier SEULEMENT displayName et phoneNumber de son propre profil.
      allow update: if hasRole('admin') ||
                      (isOwner(userId) &&
                       request.resource.data.diff(resource.data)
                           .affectedKeys()
                           .hasOnly(['displayName', 'phoneNumber']));
    }

    match /emprunts/{empruntId} {
      allow read: if isAuthenticated();

      // Création : Tout utilisateur authentifié si les données sont valides.
      allow create: if isAuthenticated() &&
                      isValidEmpruntData(request.resource.data) &&
                      request.resource.data.statut == 'Pas prêt';

      // MISE À JOUR : Uniquement par un régisseur et sans JAMAIS modifier la date de création.
      allow update: if hasRole('regisseur') &&
                      !('createdAt' in request.resource.data.diff(resource.data).affectedKeys());

      // Suppression : Uniquement par un régisseur.
      allow delete: if hasRole('regisseur');
    }

    // --- Règles par défaut pour les autres collections ---
    match /stocks/{stockId} { allow read, write: if hasRole('regisseur'); }
    match /modules/{moduleId} {
      allow read: if isAuthenticated();
      allow write: if hasRole('regisseur');
    }
    match /livraisons/{livraisonId} {
      allow read: if isAuthenticated();
      allow write: if hasRole('regisseur');
    }

    // Règle de sécurité par défaut : tout refuser.
    match /{path=**} {
      allow read, write: if false;
    }
  }
}