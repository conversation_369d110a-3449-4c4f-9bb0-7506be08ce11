import {
  initializeTestEnvironment,
  assertSucceeds,
  assertFails,
  RulesTestEnvironment,
} from '@firebase/rules-unit-testing';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
} from 'firebase/firestore';
import * as fs from 'fs';
import { resolve } from 'path';

// --- Configuration de l'environnement de test ---
const PROJECT_ID = 'sigma-nova';
let RULES;
try {
  RULES = fs.readFileSync(resolve(__dirname, '../firebase/firestore.rules'), 'utf8');
} catch (e) {
  try {
    RULES = fs.readFileSync(resolve(__dirname, '../../firebase/firestore.rules'), 'utf8');
  } catch (e) {
    try {
      RULES = fs.readFileSync('firebase/firestore.rules', 'utf8');
    } catch (e) {
      console.error('Impossible de trouver le fichier de règles Firestore. Veuillez vérifier le chemin.', e);
      throw new Error('Fichier de règles Firestore introuvable');
    }
  }
}
let testEnv: RulesTestEnvironment;

// --- Fonctions d'aide ---
const ctx = (uid?: string, claims: any = {}) => {
  return uid
    ? testEnv.authenticatedContext(uid, claims)
    : testEnv.unauthenticatedContext();
};

// --- Cycle de vie des tests ---
beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: PROJECT_ID,
    firestore: { rules: RULES, host: 'localhost', port: 8080 },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

beforeEach(async () => {
  await testEnv.clearFirestore();
});


// --- Suite de tests pour la collection "emprunts" ---
describe('Collection emprunts Rules', () => {
  // Définition des utilisateurs et des données de test
  const USER_ID = 'user_lambda_uid';
  const REGISSEUR_ID = 'regisseur_uid';
  const ADMIN_ID = 'admin_uid';

  const validEmpruntData = {
    nom: 'Test Emprunt',
    lieu: 'Test Lieu',
    dateDepart: Timestamp.fromDate(new Date('2025-01-10')),
    dateRetour: Timestamp.fromDate(new Date('2025-01-15')),
    secteur: 'PAF',
    emprunteur: USER_ID,
    statut: 'Pas prêt',
    createdAt: Timestamp.now(), // Simule la valeur serveur
  };

  // Helper pour créer un document de test en contournant les règles
  const setupEmprunt = async (empruntId = 'emprunt_test') => {
    await testEnv.withSecurityRulesDisabled(async (context) => {
      await setDoc(doc(context.firestore(), 'emprunts', empruntId), validEmpruntData);
    });
  };

  // === Tests de Lecture (Read) ===
  test('E01: Utilisateur non authentifié ne peut pas lire les emprunts', async () => {
    await setupEmprunt();
    const unauthDb = ctx().firestore();
    await assertFails(getDoc(doc(unauthDb, 'emprunts/emprunt_test')));
  });

  test('E02: Utilisateur authentifié peut lire un emprunt', async () => {
    await setupEmprunt();
    const userDb = ctx(USER_ID, { role: 'utilisateur' }).firestore();
    await assertSucceeds(getDoc(doc(userDb, 'emprunts/emprunt_test')));
  });

  // === Tests de Création (Create) ===
  test('E05: Utilisateur authentifié peut créer un emprunt valide', async () => {
    const userDb = ctx(USER_ID, { role: 'utilisateur' }).firestore();
    await assertSucceeds(setDoc(doc(userDb, 'emprunts/new_emprunt'), validEmpruntData));
  });
  
  test('E07: Création échoue si un champ requis (nom) est manquant', async () => {
    const userDb = ctx(USER_ID, { role: 'utilisateur' }).firestore();
    const invalidData = { ...validEmpruntData };
    delete (invalidData as any).nom;
    await assertFails(setDoc(doc(userDb, 'emprunts/new_emprunt'), invalidData));
  });

  test('E09: Création échoue si le statut initial n\'est pas "Pas prêt"', async () => {
    const userDb = ctx(USER_ID, { role: 'utilisateur' }).firestore();
    const invalidData = { ...validEmpruntData, statut: 'Prêt' };
    await assertFails(setDoc(doc(userDb, 'emprunts/new_emprunt'), invalidData));
  });

  // === Tests de Mise à jour (Update) ===
  test('E11: Utilisateur standard ne peut pas mettre à jour un emprunt', async () => {
    await setupEmprunt();
    const userDb = ctx(USER_ID, { role: 'utilisateur' }).firestore();
    await assertFails(updateDoc(doc(userDb, 'emprunts/emprunt_test'), { statut: 'Prêt' }));
  });
  
  test('E12 & E13: Régisseur ou Admin peut mettre à jour le statut', async () => {
    await setupEmprunt();
    const regisseurDb = ctx(REGISSEUR_ID, { role: 'regisseur' }).firestore();
    await assertSucceeds(updateDoc(doc(regisseurDb, 'emprunts/emprunt_test'), { statut: 'Prêt' }));
  });

  test('E14: La règle UPDATE autorise une mise à jour avec des dates invalides (validation déléguée aux CF)', async () => {
    await setupEmprunt();
    const regisseurDb = ctx(REGISSEUR_ID, { role: 'regisseur' }).firestore();
    // La règle a été simplifiée, elle ne valide plus la logique métier des dates.
    // Cette validation est désormais la responsabilité de la Cloud Function.
    // Le test vérifie donc que la règle autorise l'écriture.
    await assertSucceeds(updateDoc(doc(regisseurDb, 'emprunts/emprunt_test'), { dateRetour: Timestamp.fromDate(new Date('2025-01-09')) }));
  });
  
  test('E16: Mise à jour échoue si on tente de modifier createdAt', async () => {
    await setupEmprunt();
    const regisseurDb = ctx(REGISSEUR_ID, { role: 'regisseur' }).firestore();
    await assertFails(updateDoc(doc(regisseurDb, 'emprunts/emprunt_test'), { createdAt: Timestamp.now() }));
  });

  test('E17: Régisseur peut mettre à jour plusieurs champs valides simultanément', async () => {
    await setupEmprunt();
    const regisseurDb = ctx(REGISSEUR_ID, { role: 'regisseur' }).firestore();
    await assertSucceeds(updateDoc(doc(regisseurDb, 'emprunts/emprunt_test'), {
      statut: 'Prêt',
      lieu: 'Nouveau Lieu',
      nom: 'Nom Modifié',
    }));
  });

  // === Tests de Suppression (Delete) ===
  test('E18: Utilisateur standard ne peut pas supprimer un emprunt', async () => {
    await setupEmprunt();
    const userDb = ctx(USER_ID, { role: 'utilisateur' }).firestore();
    await assertFails(deleteDoc(doc(userDb, 'emprunts/emprunt_test')));
  });

  test('E19 & E20: Régisseur ou Admin peut supprimer un emprunt', async () => {
    await setupEmprunt();
    const regisseurDb = ctx(REGISSEUR_ID, { role: 'regisseur' }).firestore();
    await assertSucceeds(deleteDoc(doc(regisseurDb, 'emprunts/emprunt_test')));
    
    await setupEmprunt('emprunt_pour_admin'); // Recréer un doc pour le test admin
    const adminDb = ctx(ADMIN_ID, { role: 'admin' }).firestore();
    await assertSucceeds(deleteDoc(doc(adminDb, 'emprunts/emprunt_pour_admin')));
  });
});